{"name": "gosafe-booking-tour", "version": "1.0.0", "description": "GoSafe Booking Tour - Micro Frontend Application", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "clean": "turbo run clean", "type-check": "turbo run type-check"}, "devDependencies": {"@types/node": "^18.0.0", "turbo": "^1.10.0", "typescript": "^5.0.0"}, "packageManager": "npm@9.0.0", "dependencies": {"@radix-ui/react-label": "^2.1.7", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "crypto-js": "^4.2.0", "moment": "^2.30.1", "node-forge": "^1.3.1", "uuid": "^11.1.0"}}