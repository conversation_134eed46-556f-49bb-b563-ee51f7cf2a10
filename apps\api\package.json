{"name": "@gosafe/api", "version": "1.0.0", "description": "GoSafe Backend API Services", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@elastic/elasticsearch": "^9.1.0", "@types/handlebars": "^4.1.0", "@types/ioredis": "^5.0.0", "@types/pg": "^8.15.5", "@types/redis": "^4.0.11", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.40.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "handlebars": "^4.7.8", "helmet": "^7.0.0", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "pg": "^8.16.3", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "stripe": "^13.0.0", "typeorm": "^0.3.25", "uuid": "^9.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.4", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/node": "^20.19.9", "@types/nodemailer": "^6.4.9", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.1.6"}, "keywords": ["gosafe", "booking", "travel", "api", "microservices"], "author": "GoSafe Team", "license": "MIT"}