import { Client } from '@elastic/elasticsearch';

// Elasticsearch client
export const elasticsearchClient = new Client({
  node: process.env.ELASTICSEARCH_NODE || 'http://localhost:9200',
  auth: process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD ? {
    username: process.env.ELASTICSEARCH_USERNAME,
    password: process.env.ELASTICSEARCH_PASSWORD,
  } : undefined,
  requestTimeout: 60000,
  maxRetries: 3,
  pingTimeout: 3000,
});

// Index names
export const INDICES = {
  TOURS: 'tours',
  FLIGHTS: 'flights',
  HOTELS: 'hotels',
  NEWS: 'news',
  DESTINATIONS: 'destinations',
};

// Elasticsearch service class
export class SearchService {
  private client: Client;

  constructor(client: Client = elasticsearchClient) {
    this.client = client;
  }

  // Index management
  async createIndex(indexName: string, mapping: any): Promise<boolean> {
    try {
      const exists = await this.client.indices.exists({ index: indexName });
      
      if (!exists) {
        await this.client.indices.create({
          index: indexName,
          mappings: mapping,
          settings: {
            number_of_shards: 1,
            number_of_replicas: 0,
            analysis: {
              analyzer: {
                vietnamese_analyzer: {
                  type: 'custom',
                  tokenizer: 'standard',
                  filter: ['lowercase', 'asciifolding']
                }
              }
            }
          }
        });
        console.log(`Index ${indexName} created successfully`);
      }
      return true;
    } catch (error) {
      console.error(`Error creating index ${indexName}:`, error);
      return false;
    }
  }

  async deleteIndex(indexName: string): Promise<boolean> {
    try {
      await this.client.indices.delete({ index: indexName });
      console.log(`Index ${indexName} deleted successfully`);
      return true;
    } catch (error) {
      console.error(`Error deleting index ${indexName}:`, error);
      return false;
    }
  }

  // Document operations
  async indexDocument(indexName: string, id: string, document: any): Promise<boolean> {
    try {
      await this.client.index({
        index: indexName,
        id: id,
        body: document,
        refresh: 'wait_for'
      });
      return true;
    } catch (error) {
      console.error(`Error indexing document ${id} in ${indexName}:`, error);
      return false;
    }
  }

  async updateDocument(indexName: string, id: string, document: any): Promise<boolean> {
    try {
      await this.client.update({
        index: indexName,
        id: id,
        body: {
          doc: document
        },
        refresh: 'wait_for'
      });
      return true;
    } catch (error) {
      console.error(`Error updating document ${id} in ${indexName}:`, error);
      return false;
    }
  }

  async deleteDocument(indexName: string, id: string): Promise<boolean> {
    try {
      await this.client.delete({
        index: indexName,
        id: id,
        refresh: 'wait_for'
      });
      return true;
    } catch (error) {
      console.error(`Error deleting document ${id} from ${indexName}:`, error);
      return false;
    }
  }

  // Bulk operations
  async bulkIndex(indexName: string, documents: { id: string; doc: any }[]): Promise<boolean> {
    try {
      const body = documents.flatMap(({ id, doc }) => [
        { index: { _index: indexName, _id: id } },
        doc
      ]);

      const response = await this.client.bulk({
        body,
        refresh: 'wait_for'
      });

      if (response.errors) {
        console.error('Bulk indexing errors:', response.items);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Bulk indexing error:', error);
      return false;
    }
  }

  // Search operations
  async search(indexName: string, query: any, options: any = {}): Promise<any> {
    try {
      const response = await this.client.search({
        index: indexName,
        ...query,
        ...options
      });
      return response;
    } catch (error) {
      console.error(`Search error in ${indexName}:`, error);
      return { hits: { hits: [], total: { value: 0 } } };
    }
  }

  // Tour search
  async searchTours(searchParams: {
    query?: string;
    destination?: string;
    type?: string;
    priceRange?: { min: number; max: number };
    duration?: { min: number; max: number };
    rating?: number;
    from?: number;
    size?: number;
  }): Promise<any> {
    const { query, destination, type, priceRange, duration, rating, from = 0, size = 20 } = searchParams;

    const must: any[] = [];
    const filter: any[] = [];

    // Text search
    if (query) {
      must.push({
        multi_match: {
          query,
          fields: ['name^3', 'description^2', 'destination^2', 'shortDescription'],
          fuzziness: 'AUTO',
          analyzer: 'vietnamese_analyzer'
        }
      });
    }

    // Filters
    if (destination) {
      filter.push({ term: { 'destination.keyword': destination } });
    }

    if (type) {
      filter.push({ term: { type } });
    }

    if (priceRange) {
      filter.push({
        range: {
          price: {
            gte: priceRange.min,
            lte: priceRange.max
          }
        }
      });
    }

    if (duration) {
      filter.push({
        range: {
          duration: {
            gte: duration.min,
            lte: duration.max
          }
        }
      });
    }

    if (rating) {
      filter.push({
        range: {
          rating: { gte: rating }
        }
      });
    }

    filter.push({ term: { status: 'active' } });

    const searchQuery = {
      query: {
        bool: {
          must: must.length ? must : [{ match_all: {} }],
          filter
        }
      },
      sort: [
        { rating: { order: 'desc' } },
        { reviewCount: { order: 'desc' } },
        '_score'
      ],
      from,
      size,
      highlight: {
        fields: {
          name: {},
          description: {}
        }
      }
    };

    return await this.search(INDICES.TOURS, searchQuery);
  }

  // Flight search
  async searchFlights(searchParams: {
    from: string;
    to: string;
    departureDate: string;
    returnDate?: string;
    passengers?: number;
    class?: string;
    from_index?: number;
    size?: number;
  }): Promise<any> {
    const { from: fromAirport, to: toAirport, departureDate, returnDate, passengers = 1, class: flightClass, from_index = 0, size = 20 } = searchParams;

    const must: any[] = [
      { term: { 'departureAirport.keyword': fromAirport } },
      { term: { 'arrivalAirport.keyword': toAirport } },
      {
        range: {
          departureDate: {
            gte: departureDate,
            lte: departureDate + 'T23:59:59'
          }
        }
      },
      { term: { status: 'scheduled' } }
    ];

    if (flightClass) {
      must.push({
        range: {
          [`pricing.${flightClass}.available`]: { gte: passengers }
        }
      });
    }

    const searchQuery = {
      query: {
        bool: { must }
      },
      sort: [
        { departureDate: { order: 'asc' } },
        { [`pricing.${flightClass || 'economy'}.price`]: { order: 'asc' } }
      ],
      from: from_index,
      size
    };

    return await this.search(INDICES.FLIGHTS, searchQuery);
  }

  // Hotel search
  async searchHotels(searchParams: {
    destination: string;
    checkIn: string;
    checkOut: string;
    guests?: number;
    rooms?: number;
    category?: string;
    starRating?: number;
    priceRange?: { min: number; max: number };
    amenities?: string[];
    from?: number;
    size?: number;
  }): Promise<any> {
    const { destination, checkIn, checkOut, guests = 2, rooms = 1, category, starRating, priceRange, amenities, from = 0, size = 20 } = searchParams;

    const must = [
      {
        multi_match: {
          query: destination,
          fields: ['location.city^3', 'location.address^2', 'name^2'],
          fuzziness: 'AUTO'
        }
      },
      { term: { status: 'active' } }
    ];

    const filter: any[] = [];

    if (category) {
      filter.push({ term: { category } });
    }

    if (starRating) {
      filter.push({ term: { starRating } });
    }

    if (priceRange) {
      filter.push({
        nested: {
          path: 'rooms',
          query: {
            range: {
              'rooms.pricing.basePrice': {
                gte: priceRange.min,
                lte: priceRange.max
              }
            }
          }
        }
      });
    }

    if (amenities && amenities.length > 0) {
      amenities.forEach(amenity => {
        filter.push({
          nested: {
            path: 'amenities',
            query: {
              bool: {
                should: [
                  { term: { 'amenities.general.keyword': amenity } },
                  { term: { 'amenities.business.keyword': amenity } },
                  { term: { 'amenities.wellness.keyword': amenity } },
                  { term: { 'amenities.dining.keyword': amenity } }
                ]
              }
            }
          }
        });
      });
    }

    const searchQuery = {
      query: {
        bool: {
          must,
          filter
        }
      },
      sort: [
        { rating: { order: 'desc' } },
        { starRating: { order: 'desc' } },
        '_score'
      ],
      from,
      size
    };

    return await this.search(INDICES.HOTELS, searchQuery);
  }

  // Autocomplete suggestions
  async getSuggestions(indexName: string, field: string, text: string, size: number = 5): Promise<string[]> {
    try {
      const response = await this.client.search({
        index: indexName,
        suggest: {
          suggestions: {
            text,
            term: {
              field: `${field}.suggest`,
              size
            }
          }
        }
      });

      return (response.suggest?.suggestions as any)?.[0]?.options?.map((option: any) => option.text) || [];
    } catch (error) {
      console.error('Suggestion error:', error);
      return [];
    }
  }

  // Aggregations
  async getAggregations(indexName: string, aggregations: any): Promise<any> {
    try {
      const response = await this.client.search({
        index: indexName,
        size: 0,
        aggs: aggregations
      });
      return response.aggregations;
    } catch (error) {
      console.error('Aggregation error:', error);
      return {};
    }
  }

  // Health check
  async ping(): Promise<boolean> {
    try {
      await this.client.ping();
      return true;
    } catch (error) {
      console.error('Elasticsearch ping error:', error);
      return false;
    }
  }
}

// Default search service instance
export const searchService = new SearchService(elasticsearchClient);

// Index mappings
export const INDEX_MAPPINGS = {
  [INDICES.TOURS]: {
    properties: {
      name: { type: 'text', analyzer: 'vietnamese_analyzer' },
      description: { type: 'text', analyzer: 'vietnamese_analyzer' },
      shortDescription: { type: 'text', analyzer: 'vietnamese_analyzer' },
      status: { type: 'keyword' },
      type: { type: 'keyword' },
      difficulty: { type: 'keyword' },
      destination: { type: 'text', fields: { keyword: { type: 'keyword' } } },
      departureLocation: { type: 'text' },
      duration: { type: 'integer' },
      price: { type: 'double' },
      currency: { type: 'keyword' },
      rating: { type: 'float' },
      reviewCount: { type: 'integer' },
      images: { type: 'keyword' },
      location: {
        properties: {
          coordinates: { type: 'geo_point' },
          address: { type: 'text' },
          city: { type: 'keyword' },
          country: { type: 'keyword' }
        }
      },
      createdAt: { type: 'date' },
      updatedAt: { type: 'date' }
    }
  },

  [INDICES.FLIGHTS]: {
    properties: {
      flightNumber: { type: 'keyword' },
      airline: { type: 'keyword' },
      type: { type: 'keyword' },
      status: { type: 'keyword' },
      departureAirport: { type: 'keyword' },
      arrivalAirport: { type: 'keyword' },
      departureCity: { type: 'text', fields: { keyword: { type: 'keyword' } } },
      arrivalCity: { type: 'text', fields: { keyword: { type: 'keyword' } } },
      departureDate: { type: 'date' },
      arrivalDate: { type: 'date' },
      duration: { type: 'integer' },
      pricing: {
        properties: {
          economy: {
            properties: {
              available: { type: 'integer' },
              price: { type: 'double' }
            }
          },
          business: {
            properties: {
              available: { type: 'integer' },
              price: { type: 'double' }
            }
          }
        }
      },
      rating: { type: 'float' },
      reviewCount: { type: 'integer' },
      createdAt: { type: 'date' }
    }
  },

  [INDICES.HOTELS]: {
    properties: {
      name: { type: 'text', analyzer: 'vietnamese_analyzer' },
      description: { type: 'text', analyzer: 'vietnamese_analyzer' },
      status: { type: 'keyword' },
      category: { type: 'keyword' },
      starRating: { type: 'integer' },
      rating: { type: 'float' },
      reviewCount: { type: 'integer' },
      location: {
        properties: {
          address: { type: 'text' },
          city: { type: 'keyword' },
          country: { type: 'keyword' },
          coordinates: { type: 'geo_point' }
        }
      },
      rooms: {
        type: 'nested',
        properties: {
          type: { type: 'keyword' },
          name: { type: 'text' },
          pricing: {
            properties: {
              basePrice: { type: 'double' },
              currency: { type: 'keyword' }
            }
          },
          availability: {
            properties: {
              total: { type: 'integer' },
              available: { type: 'integer' }
            }
          }
        }
      },
      amenities: {
        type: 'nested',
        properties: {
          general: { type: 'keyword' },
          business: { type: 'keyword' },
          wellness: { type: 'keyword' },
          dining: { type: 'keyword' }
        }
      },
      createdAt: { type: 'date' },
      updatedAt: { type: 'date' }
    }
  }
};

// Initialize indices
export async function initializeElasticsearch(): Promise<void> {
  try {
    console.log('Initializing Elasticsearch indices...');
    
    for (const [indexName, mapping] of Object.entries(INDEX_MAPPINGS)) {
      await searchService.createIndex(indexName, mapping);
    }
    
    console.log('Elasticsearch initialization completed');
  } catch (error) {
    console.error('Elasticsearch initialization error:', error);
    throw error;
  }
}
