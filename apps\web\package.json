{"name": "@gosafe/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.0.0", "axios": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^4.1.0", "lucide-react": "^0.292.0", "next": "14.0.0", "react": "^18", "react-day-picker": "^9.8.1", "react-dom": "^18", "react-hook-form": "^7.47.0", "tailwind-merge": "^2.0.0", "zod": "^3.22.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10", "eslint": "^8", "eslint-config-next": "14.0.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}