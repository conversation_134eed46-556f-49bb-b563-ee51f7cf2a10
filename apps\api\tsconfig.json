{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "strictPropertyInitialization": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/controllers/*": ["src/controllers/*"], "@/models/*": ["src/models/*"], "@/services/*": ["src/services/*"], "@/middleware/*": ["src/middleware/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"]}, "allowSyntheticDefaultImports": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}